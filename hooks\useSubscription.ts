import { useState, useCallback, useEffect } from 'react';
import { useIAP } from 'expo-iap';
import { useAppStore } from '@/lib/store';
import { validateLocalReceipt } from '@/lib/utils';
import * as SecureStore from 'expo-secure-store';

// 产品ID - 需要在App Store Connect和Google Play Console中配置
const PRODUCT_ID = 'rayboxui.pro'; // 替换为实际的产品ID

/**
 * 订阅管理Hook
 *
 * 基于expo-iap的真实IAP实现，支持：
 * - 购买订阅产品
 * - 恢复购买
 * - 本地验证订阅收据
 * - 自动处理购买状态更新
 * - 错误处理和状态管理
 * - 购买验证和完成交易
 */

export interface SubscriptionHook {
  isPro: boolean;
  isLoading: boolean;
  isConnected: boolean;
  purchasePro: () => Promise<void>;
  restorePurchases: () => Promise<void>;
  validateLocalReceipt: () => Promise<boolean>;
  error: string | null;
  products: any[];
  subscriptions: any[];
  currentPurchase: any;
}

export const useSubscription = (): SubscriptionHook => {
  const { settings, updateSettings } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 使用expo-iap的useIAP hook
  const {
    connected,
    products,
    subscriptions,
    requestProducts,
    requestPurchase,
    restorePurchases: iapRestorePurchases,
    currentPurchase,
    currentPurchaseError,
    finishTransaction,
    availablePurchases,
    clearCurrentPurchase,
    clearCurrentPurchaseError,
  } = useIAP({
    onPurchaseSuccess: async (purchase) => {
      console.log('Purchase successful:', purchase);
      try {
        // 验证购买成功，更新Pro状态
        await updateSettings({ isPro: true });
        await SecureStore.setItemAsync('isPro', 'true');

        // 完成交易
        await finishTransaction({
          purchase,
          isConsumable: false // 订阅不是消耗品
        });

        setError(null);
      } catch (err) {
        console.error('Error processing successful purchase:', err);
        setError('Failed to process purchase');
      }
    },
    onPurchaseError: (purchaseError) => {
      console.error('Purchase error:', purchaseError);
      let errorMessage = 'Purchase failed';

      // 根据错误代码提供更友好的错误信息
      switch (purchaseError.code) {
        case 'E_USER_CANCELLED':
          errorMessage = 'Purchase was cancelled';
          break;
        case 'E_ITEM_UNAVAILABLE':
          errorMessage = 'Product is not available';
          break;
        case 'E_NETWORK_ERROR':
          errorMessage = 'Network error, please try again';
          break;
        case 'E_ALREADY_OWNED':
          errorMessage = 'You already own this subscription';
          // 如果已经拥有，更新状态
          updateSettings({ isPro: true });
          break;
        default:
          errorMessage = purchaseError.message || 'An unexpected error occurred';
      }

      setError(errorMessage);
    },
    shouldAutoSyncPurchases: true, // 自动同步购买状态
  });

  // 初始化时获取产品信息
  useEffect(() => {
    if (connected) {
      console.log('[useSubscription] Store connected, requesting products...');
      // 获取订阅产品信息
      requestProducts({ skus: [PRODUCT_ID], type: 'subs' });
    } else {
      console.log('[useSubscription] Store not connected');
    }
  }, [connected, requestProducts]);

  // 检查现有购买状态
  useEffect(() => {
    if (availablePurchases && availablePurchases.length > 0) {
      console.log('[useSubscription] Available purchases:', availablePurchases.length);
      const hasProSubscription = availablePurchases.some(
        purchase => purchase.id === PRODUCT_ID
      );

      if (hasProSubscription && !settings.isPro) {
        console.log('[useSubscription] Found valid Pro subscription, updating status');
        // 发现有效的订阅，更新状态
        updateSettings({ isPro: true });
        SecureStore.setItemAsync('isPro', 'true');
      }
    }
  }, [availablePurchases, settings.isPro, updateSettings]);

  // 处理当前购买错误
  useEffect(() => {
    if (currentPurchaseError) {
      console.log('[useSubscription] Purchase error:', currentPurchaseError);
      setIsLoading(false);
    }
  }, [currentPurchaseError]);

  // 处理当前购买成功
  useEffect(() => {
    if (currentPurchase) {
      console.log('[useSubscription] Purchase successful:', currentPurchase.id);
      setIsLoading(false);
    }
  }, [currentPurchase]);

  // 调试信息 - 订阅状态变化
  useEffect(() => {
    if (__DEV__) {
      console.log('[useSubscription] Debug Info:', {
        connected,
        isPro: settings.isPro,
        isLoading,
        productsCount: products.length,
        subscriptionsCount: subscriptions.length,
        error,
        hasCurrentPurchase: !!currentPurchase,
        availablePurchasesCount: availablePurchases.length
      });
    }
  }, [connected, settings.isPro, isLoading, products.length, subscriptions.length, error, currentPurchase, availablePurchases.length]);

  // 购买Pro版本
  const purchasePro = useCallback(async (): Promise<void> => {
    if (!connected) {
      console.log('[useSubscription] Purchase failed: Store not connected');
      setError('Store connection not available');
      return;
    }

    console.log('[useSubscription] Starting purchase for product:', PRODUCT_ID);
    setIsLoading(true);
    setError(null);
    clearCurrentPurchase();
    clearCurrentPurchaseError();

    try {
      await requestPurchase({
        request: {
          ios: { sku: PRODUCT_ID },
          android: {
            skus: [PRODUCT_ID],
            subscriptionOffers: [{ sku: PRODUCT_ID, offerToken: '' }]
          }
        },
        type: 'subs'
      });
      console.log('[useSubscription] Purchase request sent successfully');
    } catch (err) {
      console.error('[useSubscription] Purchase request failed:', err);
      setError(err instanceof Error ? err.message : 'Purchase failed');
      setIsLoading(false);
      throw err;
    }
  }, [connected, requestPurchase, clearCurrentPurchase, clearCurrentPurchaseError]);

  // 恢复购买
  const restorePurchases = useCallback(async (): Promise<void> => {
    if (!connected) {
      console.log('[useSubscription] Restore failed: Store not connected');
      setError('Store connection not available');
      return;
    }

    console.log('[useSubscription] Starting restore purchases');
    setIsLoading(true);
    setError(null);

    try {
      await iapRestorePurchases();
      console.log('[useSubscription] Purchases restored successfully');
    } catch (err) {
      console.error('[useSubscription] Restore purchases failed:', err);
      setError(err instanceof Error ? err.message : 'Restore failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [connected, iapRestorePurchases]);

  // 本地验证订阅收据 - 调用 utils 中的函数
  const validateLocalReceiptWrapper = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await validateLocalReceipt(availablePurchases);
      return result;
    } catch (err) {
      console.error('[useSubscription] Local receipt validation failed:', err);
      setError(err instanceof Error ? err.message : 'Receipt validation failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [availablePurchases]);

  return {
    isPro: settings.isPro,
    isLoading,
    isConnected: connected,
    purchasePro,
    restorePurchases,
    validateLocalReceipt,
    error,
    products,
    subscriptions,
    currentPurchase,
  };
};
