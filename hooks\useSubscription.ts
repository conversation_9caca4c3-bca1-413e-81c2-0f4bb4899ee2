import { useState, useCallback, useEffect } from 'react';
import { useIAP, validateReceipt } from 'expo-iap';
import { useAppStore } from '@/lib/store';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// 产品ID - 需要在App Store Connect和Google Play Console中配置
const PRODUCT_ID = 'rayboxui.pro'; // 替换为实际的产品ID

/**
 * 订阅管理Hook
 *
 * 基于expo-iap的真实IAP实现，支持：
 * - 购买订阅产品
 * - 恢复购买
 * - 自动处理购买状态更新
 * - 错误处理和状态管理
 * - 购买验证和完成交易
 */

export interface SubscriptionHook {
  isPro: boolean;
  isLoading: boolean;
  isConnected: boolean;
  purchasePro: () => Promise<void>;
  restorePurchases: () => Promise<void>;
  validateLocalReceipt: () => Promise<boolean>;
  error: string | null;
  products: any[];
  subscriptions: any[];
  currentPurchase: any;
}

export const useSubscription = (): SubscriptionHook => {
  const { settings, updateSettings } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 使用expo-iap的useIAP hook
  const {
    connected,
    products,
    subscriptions,
    requestProducts,
    requestPurchase,
    restorePurchases: iapRestorePurchases,
    currentPurchase,
    currentPurchaseError,
    finishTransaction,
    availablePurchases,
    clearCurrentPurchase,
    clearCurrentPurchaseError,
  } = useIAP({
    onPurchaseSuccess: async (purchase) => {
      console.log('Purchase successful:', purchase);
      try {
        // 验证购买成功，更新Pro状态
        await updateSettings({ isPro: true });
        await SecureStore.setItemAsync('isPro', 'true');

        // 完成交易
        await finishTransaction({
          purchase,
          isConsumable: false // 订阅不是消耗品
        });

        setError(null);
      } catch (err) {
        console.error('Error processing successful purchase:', err);
        setError('Failed to process purchase');
      }
    },
    onPurchaseError: (purchaseError) => {
      console.error('Purchase error:', purchaseError);
      let errorMessage = 'Purchase failed';

      // 根据错误代码提供更友好的错误信息
      switch (purchaseError.code) {
        case 'E_USER_CANCELLED':
          errorMessage = 'Purchase was cancelled';
          break;
        case 'E_ITEM_UNAVAILABLE':
          errorMessage = 'Product is not available';
          break;
        case 'E_NETWORK_ERROR':
          errorMessage = 'Network error, please try again';
          break;
        case 'E_ALREADY_OWNED':
          errorMessage = 'You already own this subscription';
          // 如果已经拥有，更新状态
          updateSettings({ isPro: true });
          break;
        default:
          errorMessage = purchaseError.message || 'An unexpected error occurred';
      }

      setError(errorMessage);
    },
    shouldAutoSyncPurchases: true, // 自动同步购买状态
  });

  // 初始化时获取产品信息
  useEffect(() => {
    if (connected) {
      console.log('[useSubscription] Store connected, requesting products...');
      // 获取订阅产品信息
      requestProducts({ skus: [PRODUCT_ID], type: 'subs' });
    } else {
      console.log('[useSubscription] Store not connected');
    }
  }, [connected, requestProducts]);

  // 检查现有购买状态
  useEffect(() => {
    if (availablePurchases && availablePurchases.length > 0) {
      console.log('[useSubscription] Available purchases:', availablePurchases.length);
      const hasProSubscription = availablePurchases.some(
        purchase => purchase.id === PRODUCT_ID
      );

      if (hasProSubscription && !settings.isPro) {
        console.log('[useSubscription] Found valid Pro subscription, updating status');
        // 发现有效的订阅，更新状态
        updateSettings({ isPro: true });
        SecureStore.setItemAsync('isPro', 'true');
      }
    }
  }, [availablePurchases, settings.isPro, updateSettings]);

  // 处理当前购买错误
  useEffect(() => {
    if (currentPurchaseError) {
      console.log('[useSubscription] Purchase error:', currentPurchaseError);
      setIsLoading(false);
    }
  }, [currentPurchaseError]);

  // 处理当前购买成功
  useEffect(() => {
    if (currentPurchase) {
      console.log('[useSubscription] Purchase successful:', currentPurchase.id);
      setIsLoading(false);
    }
  }, [currentPurchase]);

  // 调试信息 - 订阅状态变化
  useEffect(() => {
    if (__DEV__) {
      console.log('[useSubscription] Debug Info:', {
        connected,
        isPro: settings.isPro,
        isLoading,
        productsCount: products.length,
        subscriptionsCount: subscriptions.length,
        error,
        hasCurrentPurchase: !!currentPurchase,
        availablePurchasesCount: availablePurchases.length
      });
    }
  }, [connected, settings.isPro, isLoading, products.length, subscriptions.length, error, currentPurchase, availablePurchases.length]);

  // 购买Pro版本
  const purchasePro = useCallback(async (): Promise<void> => {
    if (!connected) {
      console.log('[useSubscription] Purchase failed: Store not connected');
      setError('Store connection not available');
      return;
    }

    console.log('[useSubscription] Starting purchase for product:', PRODUCT_ID);
    setIsLoading(true);
    setError(null);
    clearCurrentPurchase();
    clearCurrentPurchaseError();

    try {
      await requestPurchase({
        request: {
          ios: { sku: PRODUCT_ID },
          android: {
            skus: [PRODUCT_ID],
            subscriptionOffers: [{ sku: PRODUCT_ID, offerToken: '' }]
          }
        },
        type: 'subs'
      });
      console.log('[useSubscription] Purchase request sent successfully');
    } catch (err) {
      console.error('[useSubscription] Purchase request failed:', err);
      setError(err instanceof Error ? err.message : 'Purchase failed');
      setIsLoading(false);
      throw err;
    }
  }, [connected, requestPurchase, clearCurrentPurchase, clearCurrentPurchaseError]);

  // 恢复购买
  const restorePurchases = useCallback(async (): Promise<void> => {
    if (!connected) {
      console.log('[useSubscription] Restore failed: Store not connected');
      setError('Store connection not available');
      return;
    }

    console.log('[useSubscription] Starting restore purchases');
    setIsLoading(true);
    setError(null);

    try {
      await iapRestorePurchases();
      console.log('[useSubscription] Purchases restored successfully');
    } catch (err) {
      console.error('[useSubscription] Restore purchases failed:', err);
      setError(err instanceof Error ? err.message : 'Restore failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [connected, iapRestorePurchases]);

  // 本地验证订阅收据
  const validateLocalReceipt = useCallback(async (): Promise<boolean> => {
    if (!connected) {
      console.log('[useSubscription] Validate failed: Store not connected');
      setError('Store connection not available');
      return false;
    }

    console.log('[useSubscription] Starting local receipt validation');
    setIsLoading(true);
    setError(null);

    try {
      if (Platform.OS === 'ios') {
        // iOS 使用 validateReceipt 进行本地验证
        const result = await validateReceipt(PRODUCT_ID);
        console.log('[useSubscription] iOS receipt validation result:', result);

        if (result && result.isValid) {
          // 验证成功，更新Pro状态
          if (!settings.isPro) {
            await updateSettings({ isPro: true });
            await SecureStore.setItemAsync('isPro', 'true');
            console.log('[useSubscription] Local validation successful, updated Pro status');
          }
          return true;
        } else {
          // 验证失败，可能订阅已过期
          if (settings.isPro) {
            await updateSettings({ isPro: false });
            await SecureStore.setItemAsync('isPro', 'false');
            console.log('[useSubscription] Local validation failed, removed Pro status');
          }
          return false;
        }
      } else if (Platform.OS === 'android') {
        // Android 需要额外的参数进行验证，这里我们使用 availablePurchases 作为替代
        // 因为 validateReceipt 在 Android 上需要服务器端的 access token
        console.log('[useSubscription] Android: Using availablePurchases for local validation');

        const hasValidSubscription = availablePurchases && availablePurchases.length > 0 &&
          availablePurchases.some(purchase => purchase.id === PRODUCT_ID);

        if (hasValidSubscription && !settings.isPro) {
          await updateSettings({ isPro: true });
          await SecureStore.setItemAsync('isPro', 'true');
          console.log('[useSubscription] Android local validation successful, updated Pro status');
          return true;
        } else if (!hasValidSubscription && settings.isPro) {
          await updateSettings({ isPro: false });
          await SecureStore.setItemAsync('isPro', 'false');
          console.log('[useSubscription] Android local validation failed, removed Pro status');
          return false;
        }

        return hasValidSubscription;
      } else {
        throw new Error('Unsupported platform for receipt validation');
      }
    } catch (err) {
      console.error('[useSubscription] Local receipt validation failed:', err);
      setError(err instanceof Error ? err.message : 'Receipt validation failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [connected, settings.isPro, updateSettings, availablePurchases]);

  // 自动验证订阅状态 - 在连接建立后进行一次验证
  useEffect(() => {
    if (connected && !isLoading && settings.isPro) {
      // 如果用户已经是Pro用户，定期验证订阅状态
      const verifySubscription = async () => {
        try {
          console.log('[useSubscription] Auto-verifying subscription status');
          await validateLocalReceipt();
        } catch (err) {
          console.error('[useSubscription] Auto-verification failed:', err);
        }
      };

      // 延迟执行验证，避免与其他初始化操作冲突
      const timeoutId = setTimeout(verifySubscription, 2000);
      return () => clearTimeout(timeoutId);
    }
  }, [connected, isLoading, settings.isPro, validateLocalReceipt]);

  return {
    isPro: settings.isPro,
    isLoading,
    isConnected: connected,
    purchasePro,
    restorePurchases,
    validateLocalReceipt,
    error,
    products,
    subscriptions,
    currentPurchase,
  };
};
